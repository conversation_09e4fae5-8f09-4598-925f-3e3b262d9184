/* Shared styles for PDF test files */

/* Reset and base styles */
body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    height: 100vh;
    display: flex;
    flex-direction: column;
    background-color: #f5f5f5;
}

/* Header styles */
.header {
    background-color: #2c3e50;
    color: white;
    padding: 15px 20px;
    border-bottom: 1px solid #34495e;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 20px;
}

.header h1 {
    margin: 0;
    font-size: 24px;
    color: white;
}

/* Navigation links */
.nav-link {
    color: #ecf0f1;
    text-decoration: underline;
    font-size: 16px;
    transition: color 0.3s;
}

.nav-link:hover {
    color: #3498db;
    text-decoration: underline;
}

.nav-link:visited {
    color: #bdc3c7;
}

/* Button group and button styles */
.button-group {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn, .print-button, .share-button, .share-url-button, .multi-print-button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-height: 20px;
    box-sizing: border-box;
}

.btn:hover, .print-button:hover, .share-button:hover, .share-url-button:hover, .multi-print-button:hover {
    background-color: #2980b9;
}

.btn:active, .print-button:active, .share-button:active, .share-url-button:active, .multi-print-button:active {
    background-color: #21618c;
}

.btn:disabled {
    background-color: #95a5a6;
    cursor: not-allowed;
}

/* Specific button color variations */
.btn-print, .print-button {
    background-color: #e74c3c;
}

.btn-print:hover, .print-button:hover {
    background-color: #c0392b;
}

.btn-print:active, .print-button:active {
    background-color: #a93226;
}

.share-button {
    background-color: #28a745;
}

.share-button:hover {
    background-color: #218838;
}

.share-button:active {
    background-color: #1e7e34;
}

.share-url-button {
    background-color: #17a2b8;
}

.share-url-button:hover {
    background-color: #138496;
}

.share-url-button:active {
    background-color: #117a8b;
}

.multi-print-button {
    background-color: #6f42c1;
}

.multi-print-button:hover {
    background-color: #5a32a3;
}

.multi-print-button:active {
    background-color: #4c2a85;
}

/* Content area styles */
.content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 20px;
    overflow: hidden;
}

/* Common utility classes */
.text-center {
    text-align: center;
}

.mb-20 {
    margin-bottom: 20px;
}

.p-15 {
    padding: 15px;
}

.bg-white {
    background-color: white;
}

.border-radius-8 {
    border-radius: 8px;
}

.box-shadow {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Responsive design */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-left {
        flex-direction: column;
        gap: 10px;
    }

    .button-group {
        flex-wrap: wrap;
        justify-content: center;
    }

    .btn, .print-button, .share-button, .share-url-button, .multi-print-button {
        padding: 8px 16px;
        font-size: 14px;
    }

    .header h1 {
        font-size: 20px;
    }

    .nav-link {
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .button-group {
        flex-direction: column;
        width: 100%;
    }
    
    .btn, .print-button, .share-button, .share-url-button, .multi-print-button {
        width: 100%;
        margin: 2px 0;
    }
    
    .content {
        padding: 10px;
    }
}
